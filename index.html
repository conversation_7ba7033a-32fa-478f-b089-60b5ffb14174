<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gravel Road Repair Sylva NC | GravelRoadRepairDudeWnc.com | Private Road Fixes & Drainage</title>
    <meta name="description" content="Professional gravel road repair in Sylva, Cullowhee, Waynesville & Maggie Valley NC. Private road fixes, drainage correction, washouts & compaction. Expert service by GravelRoadRepairDudeWnc.com">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand-cyan': '#00E5FF',
                        'brand-cyan-dark': '#00B8CC',
                        'brand-cyan-light': '#66F0FF'
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'float': 'float 3s ease-in-out infinite',
                        'shake': 'shake 2s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        },
                        shake: {
                            '0%, 100%': { transform: 'translateX(0)' },
                            '25%': { transform: 'translateX(-5px)' },
                            '75%': { transform: 'translateX(5px)' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(0, 229, 255, 0.3)' },
                            '100%': { boxShadow: '0 0 30px rgba(0, 229, 255, 0.6)' },
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #00E5FF 0%, #00B8CC 100%); }
        .gradient-text { background: linear-gradient(135deg, #00E5FF 0%, #00B8CC 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; }
        .neon-glow { box-shadow: 0 0 20px rgba(0, 229, 255, 0.3), 0 0 40px rgba(0, 229, 255, 0.2); }
        .neon-text { text-shadow: 0 0 10px rgba(0, 229, 255, 0.5); }
        .glass-effect { background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2); }
        .floating { animation: float 3s ease-in-out infinite; }
        .shaking { animation: shake 2s ease-in-out infinite; }
        .glowing { animation: glow 2s ease-in-out infinite alternate; }
        .quote-glow { box-shadow: 0 0 30px rgba(0, 229, 255, 0.4), 0 0 60px rgba(0, 229, 255, 0.2), 0 0 90px rgba(0, 229, 255, 0.1); }
        .road-pattern { background-image: linear-gradient(45deg, rgba(0, 229, 255, 0.1) 25%, transparent 25%), linear-gradient(-45deg, rgba(0, 229, 255, 0.1) 25%, transparent 25%), linear-gradient(45deg, transparent 75%, rgba(0, 229, 255, 0.1) 75%), linear-gradient(-45deg, transparent 75%, rgba(0, 229, 255, 0.1) 75%); background-size: 60px 60px; }
    </style>
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "Gravel Road Repair Dude WNC",
        "image": "https://gravelroadrepairdudewnc.com/images/logo.png",
        "description": "Professional gravel road repair and maintenance service in Western North Carolina",
        "address": {
            "@type": "PostalAddress",
            "addressLocality": "Sylva",
            "addressRegion": "NC",
            "addressCountry": "US"
        },
        "telephone": "(*************",
        "url": "https://gravelroadrepairdudewnc.com",
        "areaServed": ["Sylva", "Cullowhee", "Waynesville", "Maggie Valley", "Franklin", "Otto", "Whittier", "Cherokee", "Bryson City"],
        "serviceType": ["Private Road Fixes", "Drainage Correction", "Washouts", "Compaction", "Gravel Road Repair"]
    }
    </script>
</head>

<body class="font-sans text-gray-800 bg-gray-50">
    <!-- Header with Sticky Navigation -->
    <header class="sticky top-0 z-[9990] bg-gray-900/95 backdrop-blur-md shadow-xl border-b border-brand-cyan/20">
        <div class="flex justify-between items-center px-4 py-4 md:px-6 max-w-7xl mx-auto">
            <!-- Text Logo -->
            <div class="floating">
                <a href="#home" class="block">
                    <div class="text-xl md:text-2xl font-black">
                        <span class="text-brand-cyan neon-text">GravelRoadRepair</span><span class="text-white">Dude</span>
                        <div class="text-xs md:text-sm font-bold text-brand-cyan tracking-widest uppercase">Western NC</div>
                    </div>
                </a>
            </div>
            
            <div class="hidden md:flex space-x-6 items-center">
                <a href="#services" class="text-gray-300 hover:text-brand-cyan transition-all duration-300 font-medium">Services</a>
                <a href="#about" class="text-gray-300 hover:text-brand-cyan transition-all duration-300 font-medium">Why Us</a>
                <a href="#quote" class="gradient-bg text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-semibold neon-glow hover:scale-105">
                    💰 FREE QUOTE
                </a>
                <a href="tel:8285551234" class="bg-white text-gray-900 px-6 py-3 rounded-xl hover:bg-gray-100 transition-all duration-300 font-semibold hover:scale-105">
                    📞 Call Now
                </a>
            </div>
            <button id="menuToggle" aria-label="Toggle Menu" class="md:hidden focus:outline-none bg-brand-cyan p-3 rounded-lg hover:bg-brand-cyan-dark transition-colors relative z-[9995]">
                <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>
    </header>

    <!-- Overlay -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-75 z-[99998] hidden transition-opacity duration-300"></div>

    <!-- Off-Canvas Mobile Menu -->
    <div id="mobileMenu" class="fixed top-0 left-0 h-full w-80 max-w-sm bg-gray-900 text-white transform -translate-x-full transition-transform duration-300 ease-in-out z-[99999] overflow-y-auto shadow-2xl">
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
            <!-- Text Logo for Mobile Menu -->
            <div>
                <div class="text-xl font-black">
                    <span class="text-brand-cyan neon-text">GravelRoadRepair</span><span class="text-white">Dude</span>
                    <div class="text-xs font-bold text-brand-cyan tracking-widest uppercase">Western NC</div>
                </div>
            </div>
            <button id="closeMenu" aria-label="Close Menu" class="text-white hover:text-gray-300 focus:outline-none bg-red-500 hover:bg-red-600 rounded-full w-10 h-10 flex items-center justify-center transition-colors">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div class="p-6">
            <a href="#quote" class="block w-full text-center bg-brand-cyan text-white py-4 px-6 rounded-xl text-lg font-bold neon-glow mb-6 hover:bg-brand-cyan-dark transition-colors">
                💰 GET FREE QUOTE
            </a>
            
            <nav class="space-y-4">
                <a href="#services" class="block text-lg py-3 px-4 text-gray-300 hover:text-brand-cyan hover:bg-gray-800 rounded-lg transition-colors">
                    🛣️ Services
                </a>
                <a href="#about" class="block text-lg py-3 px-4 text-gray-300 hover:text-brand-cyan hover:bg-gray-800 rounded-lg transition-colors">
                    🏆 Why Choose Us
                </a>
                <a href="tel:8285551234" class="block text-lg py-3 px-4 text-gray-300 hover:text-brand-cyan hover:bg-gray-800 rounded-lg transition-colors">
                    📞 Call Now
                </a>
            </nav>
            
            <div class="mt-8 p-4 bg-gray-800 rounded-lg border border-brand-cyan/30">
                <p class="text-brand-cyan text-sm font-bold mb-2">🚛 Serving All of WNC</p>
                <p class="text-gray-300 text-sm mb-3">Sylva, North Carolina • Cullowhee, North Carolina • Waynesville, North Carolina • Maggie Valley, North Carolina • Franklin, North Carolina • Otto, North Carolina • Whittier, North Carolina • Cherokee, North Carolina • Bryson City, North Carolina</p>
                <a href="tel:8285551234" class="text-brand-cyan text-xl font-bold neon-text block">📞 (*************</a>
                <a href="mailto:<EMAIL>" class="text-gray-300 text-sm hover:text-brand-cyan transition-colors block mt-2">📧 <EMAIL></a>
            </div>
        </div>
    </div>

    <!-- Hero Section with Video Background -->
    <section id="home" class="relative h-screen flex items-center justify-center overflow-hidden z-10">
        <video autoplay muted loop class="absolute inset-0 w-full h-full object-cover z-0">
            <source src="videos/road-repair-hero.mp4" type="video/mp4">
        </video>
        <div class="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-black/80 z-10"></div>
        <div class="relative z-20 text-center text-white px-4 max-w-6xl mx-auto">
            <div class="mb-6">
                <span class="inline-block bg-brand-cyan text-white px-6 py-2 rounded-full text-sm font-bold uppercase tracking-wide animate-bounce-slow">
                    🛣️ Expert Road Repair
                </span>
            </div>
            <h1 class="text-5xl md:text-7xl font-black mb-6 leading-tight">
                Professional Gravel Road Repair in 
                <span class="gradient-text neon-text block mt-2">Western North Carolina</span>
            </h1>
            <h2 class="text-xl md:text-3xl mb-8 font-light max-w-4xl mx-auto leading-relaxed">
                🛣️ Private Road Fixes • 💧 Drainage Correction • ⚡ Emergency Repairs
                <br class="hidden md:block">
                <span class="text-brand-cyan font-semibold">Serving Sylva, Cullowhee, Waynesville, Maggie Valley, Franklin, Otto, Whittier, Cherokee & Bryson City, North Carolina</span>
            </h2>
            <div class="flex flex-col sm:flex-row gap-6 justify-center mb-8">
                <a href="#quote" class="gradient-bg text-white px-10 py-5 rounded-2xl text-xl font-black hover:scale-110 transition-all duration-300 shadow-2xl neon-glow animate-pulse-slow">
                    💰 GET FREE REPAIR QUOTE
                </a>
                <a href="tel:8285551234" class="border-3 border-brand-cyan text-brand-cyan bg-black/50 px-10 py-5 rounded-2xl text-xl font-bold hover:bg-brand-cyan hover:text-white transition-all duration-300 shadow-2xl hover:scale-110">
                    📞 CALL (*************
                </a>
            </div>
            <div class="text-center">
                <a href="#quote" class="inline-flex items-center text-brand-cyan font-bold hover:text-white transition-colors">
                    <span class="mr-2">👇 Scroll for Instant Quote</span>
                    <i class="fas fa-arrow-down animate-bounce"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Floating CTA Banner -->
    <div class="fixed bottom-4 left-4 right-4 z-40 md:hidden">
        <a href="#quote" class="block w-full gradient-bg text-white text-center py-4 rounded-2xl font-black text-lg shadow-2xl neon-glow animate-pulse">
            💰 GET YOUR FREE QUOTE NOW
        </a>
    </div>

   <!-- Intro Section with Zigzag Layout - FIXED -->
<section class="py-20 bg-gradient-to-br from-cyan-900 via-blue-900 to-black text-white relative overflow-hidden">
    <!-- Much Cleaner Background Elements -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute top-16 left-16 w-48 h-48 bg-brand-cyan rounded-full blur-3xl"></div>
        <div class="absolute bottom-16 right-16 w-36 h-36 bg-brand-cyan-light rounded-full blur-2xl"></div>
        <div class="absolute top-1/3 right-1/3 w-24 h-24 bg-brand-cyan rounded-full blur-xl"></div>
    </div>
    
    <!-- Subtle Grid Pattern (Much Cleaner) -->
    <div class="absolute inset-0 opacity-5">
        <div class="h-full w-full" style="background-image: linear-gradient(rgba(0, 229, 255, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 229, 255, 0.1) 1px, transparent 1px); background-size: 100px 100px;"></div>
    </div>
    
    <div class="max-w-7xl mx-auto px-4 relative z-10">
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-black mb-8">
                🛣️ <span class="gradient-text">Western NC's Road Repair Specialists</span>
            </h2>
            <p class="text-xl md:text-2xl leading-relaxed text-gray-300 max-w-4xl mx-auto">
                At <span class="text-brand-cyan font-bold neon-text">GravelRoadRepairDudeWnc.com</span>, we fix and maintain private gravel roads throughout 
                <span class="text-brand-cyan font-semibold">Sylva, Cullowhee, Waynesville, Maggie Valley, Franklin, Otto, Whittier, Cherokee, and Bryson City, North Carolina</span>. 
                From washouts to drainage issues, we keep your mountain roads safe and accessible year-round! 🚛💪
            </p>
        </div>

        <!-- Zigzag Benefits -->
        <div class="space-y-16">
            <!-- Row 1 - Left -->
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="floating">
                    <div class="text-6xl mb-6 text-brand-cyan">🚨</div>
                    <h3 class="text-3xl font-bold mb-4 text-brand-cyan">Emergency Repairs Available</h3>
                    <p class="text-xl text-gray-300 leading-relaxed">Road washed out by heavy rain? We respond fast to emergency calls throughout WNC. Our experienced team gets your road back to safe, drivable condition quickly.</p>
                </div>
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-cyan/30 hover:border-brand-cyan transition-all duration-300 hover:scale-105">
                    <div class="text-center">
                        <div class="text-4xl font-black text-brand-cyan mb-2">24/7</div>
                        <div class="text-gray-300">Emergency Response</div>
                    </div>
                </div>
            </div>

            <!-- Row 2 - Right -->
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-cyan/30 hover:border-brand-cyan transition-all duration-300 hover:scale-105 lg:order-1">
                    <div class="text-center">
                        <div class="text-4xl font-black text-brand-cyan mb-2">25+</div>
                        <div class="text-gray-300">Years Experience</div>
                    </div>
                </div>
                <div class="lg:order-2">
                    <div class="text-6xl mb-6 text-brand-cyan">🏔️</div>
                    <h3 class="text-3xl font-bold mb-4 text-brand-cyan">Mountain Road Experts</h3>
                    <p class="text-xl text-gray-300 leading-relaxed">We understand WNC's unique terrain challenges - steep grades, heavy runoff, freeze-thaw cycles. Our solutions are built to last in mountain conditions.</p>
                </div>
            </div>

            <!-- Row 3 - Left -->
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="text-6xl mb-6 text-brand-cyan">🛠️</div>
                    <h3 class="text-3xl font-bold mb-4 text-brand-cyan">Complete Road Solutions</h3>
                    <p class="text-xl text-gray-300 leading-relaxed">From minor repairs to full road reconstruction, we handle every aspect of gravel road maintenance. Proper grading, drainage, and material selection for lasting results.</p>
                </div>
                <div class="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-cyan/30 hover:border-brand-cyan transition-all duration-300 hover:scale-105">
                    <div class="text-center">
                        <div class="text-4xl font-black text-brand-cyan mb-2">100%</div>
                        <div class="text-gray-300">Satisfaction Guaranteed</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-16">
            <a href="#quote" class="inline-block gradient-bg text-white px-8 py-4 rounded-2xl text-lg font-black hover:scale-110 transition-all duration-300 neon-glow">
                🎯 GET MY INSTANT QUOTE
            </a>
        </div>
    </div>
</section>

    <!-- MAIN ATTRACTION - Lead Capture Form -->
    <section id="quote" class="py-24 bg-gradient-to-br from-gray-50 via-white to-cyan-50 relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M10 50L90 50M50 10L50 90" stroke="%2300E5FF" stroke-width="2" stroke-opacity="0.1"/%3E%3C/svg%3E')] opacity-50"></div>
        <div class="max-w-4xl mx-auto px-4 relative z-10">
            <!-- Attention-Grabbing Header -->
            <div class="text-center mb-12">
                <div class="inline-block bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-2 rounded-full text-sm font-bold uppercase tracking-wide mb-4 animate-pulse">
                    🚨 EMERGENCY REPAIRS - FREE QUOTES
                </div>
                <h2 class="text-5xl md:text-6xl font-black mb-6 leading-tight">
                    Get Your <span class="gradient-text">FREE</span> Road Repair Quote in 
                    <span class="text-red-500">60 Seconds!</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    🛣️ <strong>Expert Repairs</strong> • 💧 <strong>Drainage Solutions</strong> • 📞 <strong>Emergency Response</strong>
                    <br>Fill out the form below and we'll get back to you within 2 hours!
                </p>
            </div>

            <!-- The Star Form -->
            <div class="bg-white rounded-3xl p-8 md:p-12 shadow-2xl quote-glow border-2 border-brand-cyan/20 relative">
                <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                    <div class="bg-brand-cyan text-white px-8 py-3 rounded-full font-black text-lg animate-bounce">
                        🛣️ ROAD REPAIR QUOTE FORM
                    </div>
                </div>
                
                <form action="form.php" method="POST" class="space-y-8 mt-6">
                    <!-- Honeypot Fields -->
                    <input type="text" name="website" style="display:none">
                    <input type="email" name="email_confirm" style="display:none">
                    
                    <!-- Progress Bar -->
                    <div class="bg-gray-200 rounded-full h-3 mb-8">
                        <div class="gradient-bg h-3 rounded-full w-0 transition-all duration-500" id="progressBar"></div>
                    </div>
                    
                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="relative">
                            <label for="name" class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">👤 Full Name *</label>
                            <input type="text" id="name" name="name" required class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-cyan/30 focus:border-brand-cyan transition-all duration-300 text-lg font-medium" placeholder="Enter your full name">
                            <div class="absolute right-3 top-12 text-brand-cyan opacity-0 transition-opacity duration-300" id="nameCheck">✓</div>
                        </div>
                        <div class="relative">
                            <label for="phone" class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">📞 Phone Number *</label>
                            <input type="tel" id="phone" name="phone" required class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-cyan/30 focus:border-brand-cyan transition-all duration-300 text-lg font-medium" placeholder="(*************">
                            <div class="absolute right-3 top-12 text-brand-cyan opacity-0 transition-opacity duration-300" id="phoneCheck">✓</div>
                        </div>
                    </div>
                    
                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="relative">
                            <label for="email" class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">📧 Email Address *</label>
                            <input type="email" id="email" name="email" required class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-cyan/30 focus:border-brand-cyan transition-all duration-300 text-lg font-medium" placeholder="<EMAIL>">
                            <div class="absolute right-3 top-12 text-brand-cyan opacity-0 transition-opacity duration-300" id="emailCheck">✓</div>
                        </div>
                        <div>
                            <label for="material" class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">🛣️ Service Type *</label>
                            <select id="material" name="material" required class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-cyan/30 focus:border-brand-cyan transition-all duration-300 text-lg font-medium">
                                <option value="">Select Service Type</option>
                                <option value="Private Road Fixes" selected>🛣️ Private Road Fixes</option>
                                <option value="Drainage Correction">💧 Drainage Correction</option>
                                <option value="Washouts">⚡ Washout Repair</option>
                                <option value="Compaction">🏗️ Road Compaction</option>
                                <option value="Full Reconstruction">🔄 Full Reconstruction</option>
                                <option value="Emergency Repair">🚨 Emergency Repair</option>
                                <option value="Not Sure">❓ Not Sure - Help Me Choose</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="address" class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">📍 Road Location *</label>
                        <input type="text" id="address" name="address" required class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-cyan/30 focus:border-brand-cyan transition-all duration-300 text-lg font-medium" placeholder="123 Mountain Road, Sylva, NC 28779">
                    </div>
                    
                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <label for="project_type" class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">🔧 Problem Type *</label>
                            <select id="project_type" name="project_type" required class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-cyan/30 focus:border-brand-cyan transition-all duration-300 text-lg font-medium">
                                <option value="">Select Problem Type</option>
                                <option value="Potholes">🕳️ Potholes</option>
                                <option value="Washout">⚡ Washout</option>
                                <option value="Poor Drainage">💧 Poor Drainage</option>
                                <option value="Rutting">🛣️ Rutting/Ruts</option>
                                <option value="Erosion">🏔️ Erosion</option>
                                <option value="Soft Spots">🦶 Soft Spots</option>
                                <option value="General Maintenance">🔧 General Maintenance</option>
                                <option value="Other">📋 Other</option>
                            </select>
                        </div>
                        <div>
                            <label for="timeline" class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">⏰ When Do You Need It?</label>
                            <select id="timeline" name="timeline" class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-cyan/30 focus:border-brand-cyan transition-all duration-300 text-lg font-medium">
                                <option value="Emergency">🚨 EMERGENCY</option>
                                <option value="ASAP">⚡ ASAP</option>
                                <option value="This Week">📅 This Week</option>
                                <option value="Next Week">📆 Next Week</option>
                                <option value="This Month">🗓️ This Month</option>
                                <option value="Just Planning">💭 Just Planning</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="details" class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">📝 Problem Details</label>
                        <textarea id="details" name="details" rows="4" class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-cyan/30 focus:border-brand-cyan transition-all duration-300 text-lg" placeholder="Describe the road problem: How long is the affected area? Any drainage issues? Recent weather damage?"></textarea>
                    </div>
                    
                    <!-- Captcha Container -->
                    <div id="captcha-container" class="flex justify-center"></div>
                    
                    <div class="text-center">
                        <button type="submit" class="gradient-bg text-white py-6 px-12 rounded-2xl text-2xl font-black hover:scale-110 transition-all duration-300 shadow-2xl neon-glow w-full md:w-auto">
                            🚀 GET MY FREE QUOTE NOW!
                        </button>
                        <p class="mt-4 text-sm text-gray-500">
                            ⚡ <strong>Fast Response:</strong> We'll contact you within 2 hours! • 🔒 <strong>100% Secure</strong>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Trust Badges Below Form -->
            <div class="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div class="bg-white rounded-xl p-4 shadow-lg">
                    <div class="text-2xl mb-2">⭐</div>
                    <div class="font-bold text-sm">5-Star Rated</div>
                </div>
                <div class="bg-white rounded-xl p-4 shadow-lg">
                    <div class="text-2xl mb-2">🚨</div>
                    <div class="font-bold text-sm">Emergency Service</div>
                </div>
                <div class="bg-white rounded-xl p-4 shadow-lg">
                    <div class="text-2xl mb-2">💰</div>
                    <div class="font-bold text-sm">Best Prices</div>
                </div>
                <div class="bg-white rounded-xl p-4 shadow-lg">
                    <div class="text-2xl mb-2">🏔️</div>
                    <div class="font-bold text-sm">Local WNC</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section with Diagonal Layout -->
    <section id="services" class="py-20 bg-gradient-to-br from-gray-800 via-cyan-900 to-black text-white relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%2300E5FF" fill-opacity="0.1"%3E%3Cpath d="M0 40L40 0L80 40L40 80Z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
        <div class="max-w-7xl mx-auto px-4 relative z-10">
            <div class="text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-black mb-6">
                    🛣️ Professional <span class="gradient-text">Road Repair Services</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    From emergency washout repairs to complete road reconstruction - we keep WNC's private roads safe and accessible
                </p>
            </div>
            
            <!-- Diagonal Services Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
                <!-- Private Road Fixes -->
                <div class="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-cyan/30 hover:border-brand-cyan transition-all duration-300 hover:scale-105 hover:shadow-2xl transform md:rotate-2 hover:rotate-0">
                    <div class="text-center">
                        <div class="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300 shaking">
                            🛣️
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-brand-cyan">Private Road Fixes</h3>
                        <p class="text-gray-300 leading-relaxed mb-6">Complete repair and maintenance of private gravel roads. We fix potholes, ruts, and surface damage while improving overall road stability and drainage.</p>
                        <ul class="text-sm text-gray-400 mb-6 space-y-2">
                            <li>✓ Pothole repair</li>
                            <li>✓ Surface restoration</li>
                            <li>✓ Grade correction</li>
                            <li>✓ Material replenishment</li>
                        </ul>
                        <a href="#quote" class="inline-block bg-brand-cyan text-white px-6 py-3 rounded-xl font-bold hover:scale-110 transition-all duration-300">
                            Get Quote
                        </a>
                    </div>
                </div>

                <!-- Drainage Correction -->
                <div class="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-cyan/30 hover:border-brand-cyan transition-all duration-300 hover:scale-105 hover:shadow-2xl transform md:-rotate-2 hover:rotate-0">
                    <div class="text-center">
                        <div class="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300 floating">
                            💧
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-brand-cyan">Drainage Correction</h3>
                        <p class="text-gray-300 leading-relaxed mb-6">Professional drainage solutions to prevent water damage and erosion. We install proper crowning, ditches, and culverts to manage mountain runoff.</p>
                        <ul class="text-sm text-gray-400 mb-6 space-y-2">
                            <li>✓ Crown restoration</li>
                            <li>✓ Ditch cleaning</li>
                            <li>✓ Culvert installation</li>
                            <li>✓ Water diversion</li>
                        </ul>
                        <a href="#quote" class="inline-block bg-brand-cyan text-white px-6 py-3 rounded-xl font-bold hover:scale-110 transition-all duration-300">
                            Get Quote
                        </a>
                    </div>
                </div>

                <!-- Washout Repair -->
                <div class="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-cyan/30 hover:border-brand-cyan transition-all duration-300 hover:scale-105 hover:shadow-2xl transform md:-rotate-1 hover:rotate-0">
                    <div class="text-center">
                        <div class="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300 glowing">
                            ⚡
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-brand-cyan">Washout Repair</h3>
                        <p class="text-gray-300 leading-relaxed mb-6">Emergency washout repair services for storm damage. We respond quickly to restore access and prevent further erosion with proper materials and techniques.</p>
                        <ul class="text-sm text-gray-400 mb-6 space-y-2">
                            <li>✓ Emergency response</li>
                            <li>✓ Erosion control</li>
                            <li>✓ Road bed reconstruction</li>
                            <li>✓ Stabilization materials</li>
                        </ul>
                        <a href="#quote" class="inline-block bg-brand-cyan text-white px-6 py-3 rounded-xl font-bold hover:scale-110 transition-all duration-300">
                            Get Quote
                        </a>
                    </div>
                </div>

                <!-- Compaction -->
                <div class="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-cyan/30 hover:border-brand-cyan transition-all duration-300 hover:scale-105 hover:shadow-2xl transform md:rotate-1 hover:rotate-0">
                    <div class="text-center">
                        <div class="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300 shaking">
                            🏗️
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-brand-cyan">Road Compaction</h3>
                        <p class="text-gray-300 leading-relaxed mb-6">Professional compaction services to create a solid, durable road surface. We use proper equipment and techniques for long-lasting results in mountain conditions.</p>
                        <ul class="text-sm text-gray-400 mb-6 space-y-2">
                            <li>✓ Heavy equipment compaction</li>
                            <li>✓ Base stabilization</li>
                            <li>✓ Surface preparation</li>
                            <li>✓ Quality control testing</li>
                        </ul>
                        <a href="#quote" class="inline-block bg-brand-cyan text-white px-6 py-3 rounded-xl font-bold hover:scale-110 transition-all duration-300">
                            Get Quote
                        </a>
                    </div>
                </div>
            </div>

            <!-- CTA Below Services -->
            <div class="text-center mt-16">
                <a href="#quote" class="inline-block gradient-bg text-white px-12 py-6 rounded-2xl text-2xl font-black hover:scale-110 transition-all duration-300 neon-glow">
                    🎯 GET PRICING FOR ALL SERVICES
                </a>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section with Card Stack -->
    <section id="about" class="py-20 bg-white relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%2300E5FF" fill-opacity="0.05"%3E%3Cpath d="M30 10L35 25L50 25L38 35L43 50L30 42L17 50L22 35L10 25L25 25Z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>
        <div class="max-w-7xl mx-auto px-4 relative z-10">
            <div class="text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-black mb-6">
                    🏆 Why <span class="gradient-text">GravelRoadRepair Dude WNC</span> Delivers Results
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    We're the mountain road specialists who understand what it takes to build roads that last in Western NC!
                </p>
            </div>
            
            <!-- Overlapping Cards Stack -->
            <div class="max-w-5xl mx-auto relative">
                <!-- Card 1 -->
                <div class="bg-gradient-to-br from-white to-cyan-50 rounded-3xl p-8 shadow-xl border border-brand-cyan/20 mb-8 transform hover:scale-105 transition-all duration-300">
                    <div class="flex flex-col lg:flex-row items-center">
                        <div class="flex-shrink-0 mb-6 lg:mb-0 lg:mr-8">
                            <div class="bg-gradient-to-br from-brand-cyan to-brand-cyan-dark text-white rounded-full w-20 h-20 flex items-center justify-center text-3xl">
                                🚨
                            </div>
                        </div>
                        <div class="flex-1 text-center lg:text-left">
                            <h3 class="text-3xl font-bold mb-4 text-gray-800">24/7 Emergency Response</h3>
                            <p class="text-gray-600 leading-relaxed text-lg">When storms hit and roads wash out, every hour counts. We provide round-the-clock emergency response throughout WNC because we know that road access is essential for mountain communities.</p>
                        </div>
                        <div class="flex-shrink-0 mt-6 lg:mt-0 lg:ml-8">
                            <div class="text-brand-cyan text-2xl font-bold">EMERGENCY</div>
                        </div>
                    </div>
                </div>

                <!-- Card 2 - Offset -->
                <div class="bg-gradient-to-br from-cyan-50 to-white rounded-3xl p-8 shadow-xl border border-brand-cyan/20 mb-8 transform lg:translate-x-8 hover:scale-105 transition-all duration-300">
                    <div class="flex flex-col lg:flex-row items-center">
                        <div class="flex-shrink-0 mb-6 lg:mb-0 lg:mr-8">
                            <div class="bg-gradient-to-br from-brand-cyan to-brand-cyan-dark text-white rounded-full w-20 h-20 flex items-center justify-center text-3xl">
                                🏔️
                            </div>
                        </div>
                        <div class="flex-1 text-center lg:text-left">
                            <h3 class="text-3xl font-bold mb-4 text-gray-800">25+ Years Mountain Experience</h3>
                            <p class="text-gray-600 leading-relaxed text-lg">We've been building and repairing mountain roads for over two decades. We understand freeze-thaw cycles, heavy rainfall runoff, and the unique challenges that come with WNC's terrain.</p>
                        </div>
                        <div class="flex-shrink-0 mt-6 lg:mt-0 lg:ml-8">
                            <div class="text-brand-cyan text-2xl font-bold">EXPERT</div>
                        </div>
                    </div>
                </div>

                <!-- Card 3 -->
                <div class="bg-gradient-to-br from-white to-cyan-50 rounded-3xl p-8 shadow-xl border border-brand-cyan/20 mb-8 transform hover:scale-105 transition-all duration-300">
                    <div class="flex flex-col lg:flex-row items-center">
                        <div class="flex-shrink-0 mb-6 lg:mb-0 lg:mr-8">
                            <div class="bg-gradient-to-br from-brand-cyan to-brand-cyan-dark text-white rounded-full w-20 h-20 flex items-center justify-center text-3xl">
                                🛠️
                            </div>
                        </div>
                        <div class="flex-1 text-center lg:text-left">
                            <h3 class="text-3xl font-bold mb-4 text-gray-800">Complete Road Solutions</h3>
                            <p class="text-gray-600 leading-relaxed text-lg">From emergency patches to full road reconstruction, we handle every aspect of gravel road maintenance. Our comprehensive approach ensures your road investment lasts for years to come.</p>
                        </div>
                        <div class="flex-shrink-0 mt-6 lg:mt-0 lg:ml-8">
                            <div class="text-brand-cyan text-2xl font-bold">COMPLETE</div>
                        </div>
                    </div>
                </div>

                <!-- Card 4 - Offset -->
                <div class="bg-gradient-to-br from-cyan-50 to-white rounded-3xl p-8 shadow-xl border border-brand-cyan/20 transform lg:translate-x-8 hover:scale-105 transition-all duration-300">
                    <div class="flex flex-col lg:flex-row items-center">
                        <div class="flex-shrink-0 mb-6 lg:mb-0 lg:mr-8">
                            <div class="bg-gradient-to-br from-brand-cyan to-brand-cyan-dark text-white rounded-full w-20 h-20 flex items-center justify-center text-3xl">
                                💎
                            </div>
                        </div>
                        <div class="flex-1 text-center lg:text-left">
                            <h3 class="text-3xl font-bold mb-4 text-gray-800">Premium Materials & Equipment</h3>
                            <p class="text-gray-600 leading-relaxed text-lg">We use only high-grade materials and professional equipment. From proper base rock to surface treatments, every component is selected for maximum durability in mountain conditions.</p>
                        </div>
                        <div class="flex-shrink-0 mt-6 lg:mt-0 lg:ml-8">
                            <div class="text-brand-cyan text-2xl font-bold">PREMIUM</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stats Banner -->
            <div class="mt-16 gradient-bg rounded-3xl p-8 text-white text-center">
                <h3 class="text-3xl font-bold mb-8">Proven Track Record in WNC</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div>
                        <div class="text-4xl font-black mb-2">500+</div>
                        <div class="font-semibold">Roads Repaired</div>
                    </div>
                    <div>
                        <div class="text-4xl font-black mb-2">25+</div>
                        <div class="font-semibold">Years Experience</div>
                    </div>
                    <div>
                        <div class="text-4xl font-black mb-2">24/7</div>
                        <div class="font-semibold">Emergency Service</div>
                    </div>
                    <div>
                        <div class="text-4xl font-black mb-2">100%</div>
                        <div class="font-semibold">Satisfaction Rate</div>
                    </div>
                </div>
            </div>

            <!-- Final CTA -->
            <div class="text-center mt-16">
                <a href="#quote" class="inline-block gradient-bg text-white px-12 py-6 rounded-2xl text-2xl font-black hover:scale-110 transition-all duration-300 neon-glow">
                    🚀 FIX YOUR ROAD TODAY
                </a>
            </div>
        </div>
    </section>

    <!-- Local Photo Section -->
    <section class="py-20 bg-gradient-to-br from-gray-100 to-cyan-100">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-black mb-6">
                    📸 Road Repairs Throughout <span class="gradient-text">Western NC</span>
                </h2>
                <p class="text-xl text-gray-600">
                    See the quality road repair work that keeps WNC communities connected
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="group bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                    <div class="relative overflow-hidden">
                        <img src="images/road-repair-before-after.jpg" alt="Road repair project Sylva NC" class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300" loading="lazy">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-brand-cyan font-bold">📍 Sylva, NC</div>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 text-center font-medium">Complete private road reconstruction project completed in Sylva, NC</p>
                        <div class="mt-4 text-center">
                            <a href="#quote" class="text-brand-cyan font-bold hover:underline">Get Quote for Your Road →</a>
                        </div>
                    </div>
                </div>

                <div class="group bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                    <div class="relative overflow-hidden">
                        <img src="images/drainage-repair-project.jpg" alt="Drainage repair Cullowhee NC" class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300" loading="lazy">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-brand-cyan font-bold">📍 Cullowhee, NC</div>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 text-center font-medium">Professional drainage correction preventing future washouts in Cullowhee, NC</p>
                        <div class="mt-4 text-center">
                            <a href="#quote" class="text-brand-cyan font-bold hover:underline">Get Drainage Quote →</a>
                        </div>
                    </div>
                </div>

                <div class="group bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                    <div class="relative overflow-hidden">
                        <img src="images/washout-emergency-repair.jpg" alt="Emergency washout repair Waynesville NC" class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300" loading="lazy">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-brand-cyan font-bold">📍 Waynesville, NC</div>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 text-center font-medium">Emergency washout repair restored access after storm damage in Waynesville, NC</p>
                        <div class="mt-4 text-center">
                            <a href="#quote" class="text-brand-cyan font-bold hover:underline">Get Emergency Quote →</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final Mega CTA -->
    <section class="py-24 gradient-bg text-white relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M10 50L90 50M50 10L50 90" stroke="%23FFFFFF" stroke-width="3" stroke-opacity="0.1"/%3E%3C/svg%3E')] opacity-20"></div>
        <div class="max-w-5xl mx-auto px-4 text-center relative z-10">
            <div class="mb-8">
                <div class="inline-block bg-white text-brand-cyan px-6 py-3 rounded-full text-sm font-bold uppercase tracking-wide animate-pulse">
                    🚨 DON'T WAIT - ROAD DAMAGE GETS WORSE!
                </div>
            </div>
            <h2 class="text-5xl md:text-7xl font-black mb-8 leading-tight">
                Ready to Fix Your Road?
            </h2>
            <p class="text-2xl md:text-3xl mb-12 font-medium max-w-4xl mx-auto leading-relaxed">
                Join 500+ satisfied property owners who chose Western North Carolina's #1 road repair specialists!
                <br><span class="font-bold">Get your FREE quote in 60 seconds!</span>
            </p>
            <div class="flex flex-col sm:flex-row gap-8 justify-center items-center mb-8">
                <a href="#quote" class="bg-white text-brand-cyan px-12 py-6 rounded-2xl text-2xl font-black hover:scale-110 transition-all duration-300 shadow-2xl border-2 border-white hover:bg-gray-100">
                    💰 GET FREE QUOTE NOW
                </a>
                <div class="text-xl font-bold">OR</div>
                <a href="tel:8285551234" class="bg-black text-white px-12 py-6 rounded-2xl text-2xl font-black hover:scale-110 transition-all duration-300 shadow-2xl border-2 border-black hover:bg-gray-900">
                    📞 CALL (*************
                </a>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-3xl mx-auto text-sm font-bold">
                <div>✅ Emergency Service</div>
                <div>✅ 25+ Years Experience</div>
                <div>✅ Complete Solutions</div>
                <div>✅ Mountain Specialists</div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="max-w-7xl mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <!-- Text Logo for Footer -->
                    <div class="mb-6">
                        <div class="text-2xl font-black">
                            <span class="text-brand-cyan neon-text">GravelRoadRepair</span><span class="text-white">Dude</span>
                            <div class="text-sm font-bold text-brand-cyan tracking-widest uppercase">Western NC</div>
                        </div>
                    </div>
                    <p class="text-gray-300 leading-relaxed mb-4">Western North Carolina's premier gravel road repair and maintenance service. Professional, reliable, and built to last in mountain conditions.</p>
                    <div class="flex space-x-4">
                        <a href="#" target="_blank" aria-label="Facebook" class="text-brand-cyan hover:text-white transition-colors"><i class="fab fa-facebook-f text-xl"></i></a>
                        <a href="#" target="_blank" aria-label="Instagram" class="text-brand-cyan hover:text-white transition-colors"><i class="fab fa-instagram text-xl"></i></a>
                        <a href="#" target="_blank" aria-label="Google" class="text-brand-cyan hover:text-white transition-colors"><i class="fab fa-google text-xl"></i></a>
                    </div>
                </div>
                <div>
                    <h4 class="text-xl font-bold mb-6 text-brand-cyan">🛣️ Services</h4>
                    <ul class="space-y-3 text-gray-300">
                        <li><a href="#quote" class="hover:text-brand-cyan transition-colors">Private Road Fixes</a></li>
                        <li><a href="#quote" class="hover:text-brand-cyan transition-colors">Drainage Correction</a></li>
                        <li><a href="#quote" class="hover:text-brand-cyan transition-colors">Washout Repair</a></li>
                        <li><a href="#quote" class="hover:text-brand-cyan transition-colors">Road Compaction</a></li>
                        <li><a href="#quote" class="hover:text-brand-cyan transition-colors">Emergency Service</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-xl font-bold mb-6 text-brand-cyan">📍 Service Areas</h4>
                    <ul class="space-y-2 text-gray-300 text-sm">
                        <li>Sylva, North Carolina</li>
                        <li>Cullowhee, North Carolina</li>
                        <li>Waynesville, North Carolina</li>
                        <li>Maggie Valley, North Carolina</li>
                        <li>Franklin, North Carolina</li>
                        <li>Otto, North Carolina</li>
                        <li>Whittier, North Carolina</li>
                        <li>Cherokee, North Carolina</li>
                        <li>Bryson City, North Carolina</li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-xl font-bold mb-6 text-brand-cyan">📞 Contact</h4>
                    <div class="space-y-4 text-gray-300">
                        <a href="tel:8285551234" class="block hover:text-brand-cyan transition-colors">
                            <strong>📞 (*************</strong>
                        </a>
                        <a href="mailto:<EMAIL>" class="block hover:text-brand-cyan transition-colors">
                            📧 <EMAIL>
                        </a>
                        <p>🏔️ Serving All of Western NC</p>
                        <a href="#quote" class="inline-block bg-brand-cyan text-white px-6 py-3 rounded-xl font-bold hover:scale-105 transition-all duration-300 mt-4">
                            Get Quote
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
                <p>&copy; 2024 GravelRoadRepairDudeWnc.com - Professional Gravel Road Repair Throughout Western North Carolina | All Rights Reserved</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile Menu
        const menuToggle = document.getElementById('menuToggle');
        const mobileMenu = document.getElementById('mobileMenu');
        const closeMenu = document.getElementById('closeMenu');
        const overlay = document.getElementById('overlay');

        function openMenu() {
            mobileMenu.classList.remove('-translate-x-full');
            overlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeMenuHandler() {
            mobileMenu.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        if (menuToggle) menuToggle.addEventListener('click', openMenu);
        if (closeMenu) closeMenu.addEventListener('click', closeMenuHandler);
        if (overlay) overlay.addEventListener('click', closeMenuHandler);

        // Close menu when clicking on navigation links
        document.querySelectorAll('#mobileMenu a[href^="#"]').forEach(link => {
            link.addEventListener('click', closeMenuHandler);
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    closeMenuHandler();
                }
            });
        });

        // Form Progress and Validation
        const form = document.querySelector('form');
        const progressBar = document.getElementById('progressBar');
        const requiredFields = form.querySelectorAll('input[required], select[required]');

        function updateProgress() {
            let filled = 0;
            requiredFields.forEach(field => {
                if (field.value.trim() !== '') filled++;
            });
            const progress = (filled / requiredFields.length) * 100;
            progressBar.style.width = progress + '%';
        }

        // Add validation checkmarks
        const nameField = document.getElementById('name');
        const emailField = document.getElementById('email');
        const phoneField = document.getElementById('phone');

        nameField.addEventListener('input', function() {
            const check = document.getElementById('nameCheck');
            if (this.value.length >= 2) {
                check.style.opacity = '1';
            } else {
                check.style.opacity = '0';
            }
            updateProgress();
        });

        emailField.addEventListener('input', function() {
            const check = document.getElementById('emailCheck');
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (emailRegex.test(this.value)) {
                check.style.opacity = '1';
            } else {
                check.style.opacity = '0';
            }
            updateProgress();
        });

        phoneField.addEventListener('input', function() {
            const check = document.getElementById('phoneCheck');
            if (this.value.length >= 10) {
                check.style.opacity = '1';
            } else {
                check.style.opacity = '0';
            }
            updateProgress();
        });

        // Update progress on all field changes
        requiredFields.forEach(field => {
            field.addEventListener('input', updateProgress);
            field.addEventListener('change', updateProgress);
        });

        // Initial progress update
        updateProgress();

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.floating, .shaking, .glowing, .group').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    </script>
</body>
</html>